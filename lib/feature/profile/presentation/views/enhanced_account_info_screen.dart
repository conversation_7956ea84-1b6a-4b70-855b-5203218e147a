import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/theme/theme_extensions.dart';
import 'package:gather_point/core/widgets/enhanced_card.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/profile/data/services/account_api_service.dart';
import 'package:gather_point/feature/profile/data/models/account_models.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_cubit.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_state.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';

class EnhancedAccountInfoScreen extends StatefulWidget {
  const EnhancedAccountInfoScreen({super.key});

  @override
  State<EnhancedAccountInfoScreen> createState() => _EnhancedAccountInfoScreenState();
}

class _EnhancedAccountInfoScreenState extends State<EnhancedAccountInfoScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final AccountApiService _accountApiService = getIt<AccountApiService>();

  AccountInfo? _accountInfo;
  SecuritySettings? _securitySettings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAccountData();

    // Initialize ProfileCubit with user data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final profileCubit = context.read<ProfileCubit>();
      if (profileCubit.state is ProfileInitial) {
        profileCubit.loadLocalUser();
        // If no local user, load from API
        if (profileCubit.state is ProfileInitial) {
          profileCubit.loadUserInfo();
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAccountData() async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      final results = await Future.wait([
        _accountApiService.getAccountInfo(),
        _accountApiService.getSecuritySettings(),
      ]);

      if (!mounted) return;
      setState(() {
        _accountInfo = results[0] as AccountInfo;
        _securitySettings = results[1] as SecuritySettings;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(s.accountInfoTitle),
        bottom: TabBar(
          controller: _tabController,
          labelColor: context.primaryColor, // Active tab color
          unselectedLabelColor: context.secondaryTextColor, // Inactive tab color
          indicatorColor: context.primaryColor, // Tab indicator color
          indicatorWeight: 3,
          tabs: [
            Tab(text: s.personalInfoTab),
            Tab(text: s.securityTab),
            Tab(text: s.statisticsTab),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildPersonalInfoTab(),
                _buildSecurityTab(),
                _buildStatsTab(),
              ],
            ),
    );
  }

  Widget _buildPersonalInfoTab() {
    final s = S.of(context);
    if (_accountInfo == null) {
      return Center(child: Text(s.noInfoAvailable));
    }

    final user = _accountInfo!.user;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 90), // Add bottom padding for nav bar (70px + 20px extra)
      child: Column(
        children: [
          // Profile Picture Section
          EnhancedCard(
            child: Column(
              children: [
                CircleAvatar(
                  radius: 50,
                  backgroundImage: user.image.isNotEmpty
                      ? NetworkImage(user.image)
                      : null,
                  child: user.image.isEmpty
                      ? const Icon(Icons.person, size: 50)
                      : null,
                ),
                const SizedBox(height: 16),
                Text(
                  user.fullName,
                  style: AppTextStyles.font20Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                if (user.bio.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    user.bio,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Personal Information
          EnhancedCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  s.personalInfo,
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 16),

                _buildInfoItem(
                  icon: Icons.email_outlined,
                  label: s.email,
                  value: user.email,
                ),

                _buildInfoItem(
                  icon: Icons.phone_outlined,
                  label: s.phone,
                  value: user.phone,
                ),

                if (user.birthdate.isNotEmpty)
                  _buildInfoItem(
                    icon: Icons.cake_outlined,
                    label: s.birthdate,
                    value: user.birthdate,
                  ),

                _buildInfoItem(
                  icon: Icons.person_outline,
                  label: s.gender,
                  value: user.gender == 1 ? s.male : user.gender == 2 ? s.female : s.notSpecified,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Actions
          EnhancedCard(
            child: Column(
              children: [
                ListTile(
                  leading: Icon(Icons.edit, color: context.primaryColor),
                  title: Text(s.editPersonalInfo),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    final cubit = context.read<ProfileCubit>();
                    if (cubit.state is ProfileLoaded) {
                      final user = (cubit.state as ProfileLoaded).user;
                      context.push('/edit-profile', extra: {'user': user});
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(s.cannotLoadUserData),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                ),

                ListTile(
                  leading: Icon(Icons.download, color: context.primaryColor),
                  title: Text(s.exportAccountData),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _exportAccountData,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityTab() {
    final s = S.of(context);
    if (_securitySettings == null) {
      return Center(child: Text(s.noSecuritySettingsAvailable));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 90), // Add bottom padding for nav bar (70px + 20px extra)
      child: Column(
        children: [
          // Login Sessions
          EnhancedCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  s.activeSessions,
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 16),
                
                ..._securitySettings!.loginSessions.map((session) => 
                  _buildSessionItem(session)
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Recent Activities
          EnhancedCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  s.recentActivities,
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 16),
                
                ..._securitySettings!.recentActivities.map((activity) => 
                  _buildActivityItem(activity)
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsTab() {
    final s = S.of(context);
    if (_accountInfo == null) {
      return Center(child: Text(s.noStatisticsAvailable));
    }

    final stats = _accountInfo!.stats;

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 90), // Add bottom padding for nav bar (70px + 20px extra)
      child: Column(
        children: [
          // Account Stats
          EnhancedCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  s.accountStatistics,
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.calendar_today,
                        title: s.memberSinceLabel,
                        value: stats.memberSince,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.access_time,
                        title: s.lastLogin,
                        value: stats.lastLogin,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.book_online,
                        title: s.totalBookingsLabel,
                        value: stats.totalBookings.toString(),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.star,
                        title: s.totalReviewsLabel,
                        value: stats.totalReviews.toString(),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Verification Status
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: stats.isVerified 
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        stats.isVerified ? Icons.verified : Icons.warning,
                        color: stats.isVerified ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        stats.isVerified ? s.verifiedAccount : s.unverifiedAccount,
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: stats.isVerified ? Colors.green : Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: context.secondaryTextColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
                Text(
                  value.isNotEmpty ? value : S.of(context).notSpecified,
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionItem(LoginSession session) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: session.isCurrent 
            ? context.primaryColor.withValues(alpha: 0.1)
            : context.cardColor,
        borderRadius: BorderRadius.circular(8),
        border: session.isCurrent 
            ? Border.all(color: context.primaryColor)
            : null,
      ),
      child: Row(
        children: [
          Icon(
            Icons.devices,
            color: session.isCurrent ? context.primaryColor : context.secondaryTextColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  session.device,
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                Text(
                  '${session.location} • ${session.lastActive}',
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          if (session.isCurrent)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: context.primaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                S.of(context).currentSession,
                style: AppTextStyles.font10Bold.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(RecentActivity activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          Icon(
            Icons.history,
            color: context.secondaryTextColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.action,
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                Text(
                  '${activity.device} • ${activity.timestamp}',
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: context.primaryColor, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.font12Regular.copyWith(
              color: context.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _exportAccountData() async {
    final s = S.of(context);
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final exportData = await _accountApiService.exportAccountData();

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        // Show export success dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(s.dataExportedSuccessfully),
            content: Text(
              '${s.dataExportSuccessMessage}:\n'
              '• ${exportData.bookings.length} ${s.bookingsCount}\n'
              '• ${exportData.reviews.length} ${s.reviewsCount}\n'
              '• ${s.completePersonalInfo}',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(s.ok),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${s.error}: ${e.toString()}')),
        );
      }
    }
  }


}

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final AccountApiService _accountApiService = getIt<AccountApiService>();
  
  bool _isLoading = false;
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(s.changePasswordTitle),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 90), // Add bottom padding for nav bar (70px + 20px extra)
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              EnhancedCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      s.changePasswordTitle,
                      style: AppTextStyles.font18Bold.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Current Password
                    TextFormField(
                      controller: _currentPasswordController,
                      obscureText: _obscureCurrentPassword,
                      decoration: InputDecoration(
                        labelText: s.currentPassword,
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscureCurrentPassword ? Icons.visibility : Icons.visibility_off,
                          ),
                          onPressed: () {
                            if (mounted) {
                              setState(() {
                                _obscureCurrentPassword = !_obscureCurrentPassword;
                              });
                            }
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return s.enterCurrentPassword;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // New Password
                    TextFormField(
                      controller: _newPasswordController,
                      obscureText: _obscureNewPassword,
                      decoration: InputDecoration(
                        labelText: s.newPassword,
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscureNewPassword ? Icons.visibility : Icons.visibility_off,
                          ),
                          onPressed: () {
                            if (mounted) {
                              setState(() {
                                _obscureNewPassword = !_obscureNewPassword;
                              });
                            }
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return s.enterNewPassword;
                        }
                        if (value.length < 8) {
                          return s.passwordMinLength;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Confirm Password
                    TextFormField(
                      controller: _confirmPasswordController,
                      obscureText: _obscureConfirmPassword,
                      decoration: InputDecoration(
                        labelText: s.confirmNewPassword,
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                          ),
                          onPressed: () {
                            if (mounted) {
                              setState(() {
                                _obscureConfirmPassword = !_obscureConfirmPassword;
                              });
                            }
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى تأكيد كلمة المرور الجديدة';
                        }
                        if (value != _newPasswordController.text) {
                          return 'كلمة المرور غير متطابقة';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),
                    
                    // Submit Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _submitPasswordChange,
                        child: _isLoading
                            ? const CircularProgressIndicator()
                            : const Text('تغيير كلمة المرور'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _submitPasswordChange() async {
    if (!_formKey.currentState!.validate()) return;

    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      await _accountApiService.changePassword(
        currentPassword: _currentPasswordController.text,
        newPassword: _newPasswordController.text,
        confirmPassword: _confirmPasswordController.text,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تغيير كلمة المرور بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
